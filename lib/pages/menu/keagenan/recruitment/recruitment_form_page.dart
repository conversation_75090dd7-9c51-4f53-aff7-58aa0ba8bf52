import 'package:expandable_page_view/expandable_page_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/recruitment_form_controller.dart';
import 'package:pdl_superapp/pages/menu/keagenan/recruitment/form_identification.dart';
import 'package:pdl_superapp/pages/menu/keagenan/recruitment/form_self_identification.dart';
import 'package:pdl_superapp/pages/menu/keagenan/recruitment/form_terms_signature.dart';
import 'package:pdl_superapp/pages/menu/keagenan/recruitment/form_verification.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class RecruitmentFormPage extends StatelessWidget {
  RecruitmentFormPage({super.key});

  final RecruitmentFormController controller = Get.put(
    RecruitmentFormController(),
  );

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Stack(
        children: [
          _formPages(context),
          if (controller.isVerificationEmailSent.isTrue)
            verificationPages(context),
        ],
      ),
    );
  }

  BaseDetailPage verificationPages(BuildContext context) {
    return BaseDetailPage(
      title: 'Verifikasi Email Kandidat',
      controller: controller,
      onRefresh: () {},
      onBack: () {
        controller.isVerificationEmailSent.value = false;
      },
      child: Container(
        width: Get.width,
        padding: EdgeInsets.symmetric(horizontal: paddingMedium),
        child: Column(
          children: [
            SizedBox(height: paddingExtraLarge),
            Utils.cachedSvgWrapper(
              'icon/illustration-empty-inbox.svg',
              width: 200,
            ),
            SizedBox(height: paddingMedium),
            Text(
              'Segera Verifikasi Email Kandidat',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: paddingMedium),
            Text('Tautan verifikasi telah dikirimkan ke'),
            Text(
              '<EMAIL>',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
            ),
            SizedBox(height: paddingMedium),
            Text(
              'Belum mendapatkan tautan verifikasi?',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color:
                    Get.isDarkMode ? kColorTextTersierLight : kColorTextTersier,
              ),
            ),
            SizedBox(height: paddingSmall),
            Text(
              'Kirim Ulang',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: kColorGlobalBlue),
            ),
          ],
        ),
      ),
    );
  }

  BaseDetailPage _formPages(BuildContext context) {
    return BaseDetailPage(
      title: 'Formulir Rekrutmen',
      controller: controller,
      onRefresh: () {},
      bottomWidget: Obx(
        () => Row(
          children: [
            Expanded(
              child: PdlButton(
                backgroundColor: Colors.transparent,
                foregorundColor: kColorGlobalBlue,
                title:
                    controller.activePage.value == 3
                        ? 'button_submit'.tr
                        : 'button_save'.tr,
                onPressed: () async {
                  if (controller.activePage.value == 3) {
                    // Submit form
                    final result = await controller.submitForm();
                    if (result) {
                      Utils.popup(
                        body: 'Form berhasil disimpan dan disubmit',
                        type: kPopupSuccess,
                      );
                      // Kembali ke halaman sebelumnya
                      Get.back();
                    } else {
                      Utils.popup(
                        body: 'Gagal menyimpan form',
                        type: kPopupFailed,
                      );
                    }
                  } else {
                    // Simpan form
                    final result = await controller.saveFormData();
                    if (result) {
                      Utils.popup(
                        body: 'Form berhasil disimpan',
                        type: kPopupSuccess,
                      );
                    } else {
                      Utils.popup(
                        body: 'Gagal menyimpan form',
                        type: kPopupFailed,
                      );
                    }
                  }
                },
              ),
            ),
            SizedBox(width: paddingSmall),
            Expanded(
              child: Obx(
                () => PdlButton(
                  title: 'button_next'.tr,
                  onPressed:
                      controller.activePage.value < 2
                          ? () {
                            // Validate current page before proceeding
                            if (controller.validateCurrentPage()) {
                              controller.pageController.nextPage(
                                duration: Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                              );
                              controller.activePage.value += 1;
                            } else {
                              Utils.popup(
                                body:
                                    'Mohon lengkapi semua field yang wajib diisi',
                                type: kPopupFailed,
                              );
                            }
                          }
                          : controller.activePage.value == 2
                          ? () {
                            // Validate final page before submission
                            if (controller.validateCurrentPage()) {
                              controller.submitFormForVerification();
                            } else {
                              Utils.popup(
                                body:
                                    'Mohon lengkapi semua field yang wajib diisi',
                                type: kPopupFailed,
                              );
                            }
                          }
                          : null,
                ),
              ),
            ),
          ],
        ),
      ),

      child: Container(
        padding: EdgeInsets.all(paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _pageCounter(context),
            SizedBox(height: paddingMedium),
            Obx(
              () =>
                  controller.isReady.isFalse
                      ? Center(child: CircularProgressIndicator())
                      : ExpandablePageView(
                        controller: controller.pageController,
                        children: [
                          FormVerification(
                            controller: controller.verificationController,
                          ),
                          FormIdentification(
                            controller: controller.identificationController,
                          ),
                          FormSelfIdentification(
                            controller: controller.selfIdentificationController,
                          ),
                          FormTermsSignature(controller: controller),
                        ],
                      ),
            ),
          ],
        ),
      ),
    );
  }

  SizedBox _pageCounter(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Obx(
        () => Row(
          children: [
            for (int i = 0; i < 4; i++)
              controller.activePage.value == i
                  ? Expanded(child: _pageCounterItem(context, i))
                  : _pageCounterItem(context, i),
          ],
        ),
      ),
    );
  }

  Widget _pageCounterItem(BuildContext context, int index) {
    return Obx(() {
      String title = 'Verifikasi Identitas';
      switch (index) {
        case 1:
          title = 'Data Diri Sesuai KTP';
          break;
        case 2:
          title = 'Kelengkapan Data';
          break;
        case 3:
          title = 'Perjanjian Keagenan';
          break;
        default:
      }
      return Row(
        children: [
          if (index < controller.activePage.value)
            GestureDetector(
              onTap: () {
                controller.activePage.value = index;
                controller.pageController.animateToPage(
                  index,
                  duration: Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              },
              child: Icon(Icons.check_circle, color: kColorGlobalGreen),
            ),
          if (index >= controller.activePage.value)
            Container(
              padding: EdgeInsets.all(paddingSmall),
              decoration: BoxDecoration(
                color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
                shape: BoxShape.circle,
              ),
              child: Text(
                '${index + 1}',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
              ),
            ),
          SizedBox(width: paddingSmall),
          if (controller.activePage.value == index)
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
            ),
          if (controller.activePage.value == index)
            SizedBox(width: paddingMedium),
          if (controller.activePage.value == index) Expanded(child: Divider()),
          if (controller.activePage.value == index)
            SizedBox(width: paddingMedium),
        ],
      );
    });
  }
}
